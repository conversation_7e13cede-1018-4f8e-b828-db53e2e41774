/**
 * Domain Tools for FastMCP
 * 简化的域名批量搜索工具
 */

import { z } from "zod";
import { batchSearchDomains, BatchDomainSearchOptions } from "./search.js";

// ============================================================================
// Tool Definition - Simplified
// ============================================================================

// Batch domain search schema - 简化版本
const batchDomainSearchSchema = z.object({
    keywords: z
        .array(z.string().min(1))
        .min(1)
        .max(5)
        .describe("Array of domain keywords to search (max 5 for performance). Examples: ['myapp', 'startup', 'tech']"),
    mode: z
        .enum(["available", "registered"])
        .default("available")
        .describe("Search mode: 'available' for unregistered domains, 'registered' for registered domains"),
    concurrent: z
        .boolean()
        .default(true)
        .describe("Whether to run searches concurrently for faster results")
});

// ============================================================================
// Tool Implementation
// ============================================================================

// 唯一的批量域名搜索工具
export const domainBatchSearchTool = {
    name: "domain-batch-search",
    description:
        "Search multiple domain keywords concurrently. Maximum 5 keywords per batch for optimal performance.",
    parameters: batchDomainSearchSchema,
    execute: async (args: z.infer<typeof batchDomainSearchSchema>) => {
        const { keywords, mode, concurrent } = args;
        // 使用内置的所有后缀，不允许外部传入
        const options: BatchDomainSearchOptions = {
            keywords,
            suffixes: undefined, // 使用默认的所有后缀
            mode,
            concurrent,
            timeout: 30000
        };

        const results = await batchSearchDomains(options);

        // Format results as string for FastMCP compatibility
        return results.map(summary => {
            const statusLabel = mode === 'available' ? 'Available' : 'Registered';
            return `## ${statusLabel} domains search results\n` +
                `Total checked: ${summary.totalChecked}\n` +
                `Available: ${summary.availableCount}, Registered: ${summary.registeredCount}\n` +
                `Results: ${summary.results.length}\n\n` +
                summary.results.map(domain =>
                    `- ${domain.domain} (${domain.status}) ${domain.price ? `- $${domain.price}` : ''}`
                ).join('\n') + '\n';
        }).join('\n---\n\n');
    }
};